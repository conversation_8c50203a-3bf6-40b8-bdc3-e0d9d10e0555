# Generated by Django 4.2 on 2025-06-03 18:43

import ConferenceApp.models
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('ConferenceApp', '0002_reservation'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='category',
            options={'verbose_name_plural': 'Categories'},
        ),
        migrations.AddField(
            model_name='conference',
            name='reservations',
            field=models.ManyToManyField(related_name='reservations', through='ConferenceApp.Reservation', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='category',
            name='title',
            field=models.CharField(max_length=200, validators=[django.core.validators.RegexValidator('^[A-Za-z]*$', 'only letters')]),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='conference',
            name='start_date',
            field=models.DateTimeField(default=django.utils.timezone.now, validators=[ConferenceApp.models.startDateValidator]),
        ),
    ]
