Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFBBB0, 0007FFFFAAB0) msys-2.0.dll+0x1FE8E
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210286019, 0007FFFFBA68, 0007FFFFBBB0, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBBB0  000210068E24 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBE90  00021006A225 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBCA570000 ntdll.dll
7FFBC9860000 KERNEL32.DLL
7FFBC7C90000 KERNELBASE.dll
7FFBCA170000 USER32.dll
7FFBC84C0000 win32u.dll
000210040000 msys-2.0.dll
7FFBC9580000 GDI32.dll
7FFBC8090000 gdi32full.dll
7FFBC81B0000 msvcp_win.dll
7FFBC7F90000 ucrtbase.dll
7FFBC8780000 advapi32.dll
7FFBC9180000 msvcrt.dll
7FFBC9B60000 sechost.dll
7FFBC8630000 RPCRT4.dll
7FFBC7C10000 bcrypt.dll
7FFBC7590000 CRYPTBASE.DLL
7FFBC8430000 bcryptPrimitives.dll
7FFBC9150000 IMM32.DLL
