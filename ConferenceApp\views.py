from django.shortcuts import render
from .models import Conference
from django.views.generic import ListVie<PERSON>, DetailView, CreateView, DeleteView
from django.urls import reverse_lazy

# Create your views here.
def conferenceList(request):
    list = Conference.objects.all()
    return render(request, 'conferences/ConferenceList.html', {'conferences': list})

class ConferenceListView(ListView):
    model = Conference
    template_name = 'conferences/ConferenceList.html'
    context_object_name = "conferences"
    
class ConferenceDetailView(DetailView):
    model = Conference
    template_name = 'conferences/ConferenceDetail.html'
    context_object_name = "conference"
    
class ConferenceCreateView(CreateView):
    model = Conference
    template_name = 'conferences/Conference_form.html'
    fields= ['title', 'description', 'start_date', 'end_date', 'location', 'price', 'capacity', 'program', 'category']
    success_url= reverse_lazy('conference_list')
    
class ConferenceDeleteView(DeleteView):
    model = Conference
    template_name = 'conferences/conference_confirm_delete.html'
    success_url = reverse_lazy('conference_listLV')